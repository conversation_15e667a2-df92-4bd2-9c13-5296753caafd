{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = () => [];\nfunction RequirementManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_nb_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_56_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(85);\n      return i0.ɵɵresetView(ctx_r4.add(dialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_82_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_82_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const data_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(85);\n      return i0.ɵɵresetView(ctx_r4.onEdit(data_r8, dialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_82_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_82_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const data_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onDelete(data_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 34)(1, \"td\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 36);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 36);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 36);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 36);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 36);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 36);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 35);\n    i0.ɵɵtemplate(20, RequirementManagementComponent_tr_82_button_20_Template, 3, 0, \"button\", 37)(21, RequirementManagementComponent_tr_82_button_21_Template, 3, 0, \"button\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getHouseType(data_r8.CHouseType || i0.ɵɵpureFunction0(14, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 10, data_r8.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsShowText(data_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 12, data_r8.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_84_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_84_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_84_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const b_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", b_r11.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", b_r11.CBuildCaseName, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_84_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r12.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r12.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_84_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r13.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r13.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_84_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 43)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_84_span_2_Template, 2, 0, \"span\", 44)(3, RequirementManagementComponent_ng_template_84_span_3_Template, 2, 0, \"span\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 45)(5, \"div\", 4)(6, \"div\", 46)(7, \"div\", 4)(8, \"app-form-group\", 47)(9, \"nb-select\", 48);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_84_Template_nb_select_selectedChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CBuildCaseID, $event) || (ctx_r4.saveRequirement.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(10, RequirementManagementComponent_ng_template_84_nb_option_10_Template, 2, 2, \"nb-option\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"app-form-group\", 47)(12, \"input\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_84_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRequirement, $event) || (ctx_r4.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"app-form-group\", 47)(14, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_84_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CGroupName, $event) || (ctx_r4.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"app-form-group\", 47)(16, \"input\", 52);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_84_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CSort, $event) || (ctx_r4.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 47)(18, \"nb-select\", 53);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_84_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CHouseType, $event) || (ctx_r4.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_84_nb_option_19_Template, 2, 2, \"nb-option\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 47)(21, \"nb-select\", 54);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_84_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CStatus, $event) || (ctx_r4.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, RequirementManagementComponent_ng_template_84_nb_option_22_Template, 2, 2, \"nb-option\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"app-form-group\", 47)(24, \"input\", 55);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_84_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnitPrice, $event) || (ctx_r4.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"app-form-group\", 47)(26, \"input\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_84_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnit, $event) || (ctx_r4.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 47)(28, \"nb-checkbox\", 57);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_84_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsShow, $event) || (ctx_r4.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u986F\\u793A\\u5728\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 47)(31, \"textarea\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_84_Template_textarea_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRemark, $event) || (ctx_r4.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\")(33, \"div\", 4)(34, \"div\", 59)(35, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_84_Template_button_click_35_listener() {\n      const ref_r14 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.save(ref_r14));\n    });\n    i0.ɵɵtext(36, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_84_Template_button_click_37_listener() {\n      const ref_r14 = i0.ɵɵrestoreView(_r10).dialogRef;\n      return i0.ɵɵresetView(ref_r14.close());\n    });\n    i0.ɵɵtext(38, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\\u540D\\u7A31\")(\"labelFor\", \"CBuildCaseID\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.buildCaseList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\")(\"labelFor\", \"CIsShow\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRemark);\n  }\n}\nexport class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.currentTab = 0; // 追蹤當前選中的 tab\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.getListRequirementRequest.CBuildCaseID = -1;\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CGroupName = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 根據當前 tab 決定是否需要驗證建案名稱\n    if (this.currentTab === 0) {\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    }\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 根據當前 tab 決定是否需要建案ID\n    if (this.currentTab === 0) {\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    } else {\n      // 模板頁面 - 設定建案ID為0\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\n    if (this.currentTab === 1) {\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 載入建案列表後，如果有建案資料，預設選擇第一個\n      if (this.buildCaseList.length > 0) {\n        // 使用 setTimeout 確保在下一個變更檢測週期中更新\n        setTimeout(() => {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n          // 只在初始載入時執行查詢\n          this.getList();\n        }, 0);\n      } else {\n        // 如果沒有建案資料，直接執行查詢\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      // 建案頁面的邏輯保持不變\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.requirementList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          // TODO: 等後端API更新後啟用這行\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  // Tab 切換事件處理\n  onTabChange(event) {\n    // 根據 tabTitle 來判斷當前頁面\n    if (event.tabTitle === '共用') {\n      this.currentTab = 1;\n    } else {\n      this.currentTab = 0;\n    }\n    // 當切換到模板頁面時，重置建案選擇並設為不可選\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n      this.getList(); // 切換 tab 時重新載入列表\n    } else {\n      // 切換回建案頁面時，如果沒有選擇建案且有建案資料，預設選擇第一個\n      if (this.getListRequirementRequest.CBuildCaseID === 0 || this.getListRequirementRequest.CBuildCaseID === -1) {\n        if (this.buildCaseList && this.buildCaseList.length > 0) {\n          setTimeout(() => {\n            this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n            this.getList(); // 切換 tab 時重新載入列表\n          }, 0);\n        } else {\n          this.getListRequirementRequest.CBuildCaseID = -1;\n          this.getList(); // 切換 tab 時重新載入列表\n        }\n      } else {\n        this.getList(); // 切換 tab 時重新載入列表\n      }\n    }\n  }\n  // 新增模板\n  addTemplate(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 模板設定建案ID為0\n    this.saveRequirement.CBuildCaseID = 0;\n    this.dialogService.open(dialog);\n  }\n  // 編輯模板\n  onEditTemplate(data, dialog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this2.isNew = false;\n      try {\n        yield _this2.getData();\n        _this2.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get template data\", error);\n      }\n    })();\n  }\n  // 保存模板\n  saveTemplate(ref) {\n    // 模板驗證（不包含建案名稱）\n    this.valid.clear();\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 確保模板建案ID為0\n    const templateData = {\n      ...this.saveRequirement\n    };\n    templateData.CBuildCaseID = 0;\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: templateData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  static {\n    this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementManagementComponent,\n      selectors: [[\"app-requirement-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 86,\n      vars: 20,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [\"for\", \"isShow\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CBuildCaseID\", \"name\", \"CBuildCaseID\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CGroupName\", \"name\", \"CGroupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u6392\\u5E8F\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CUnit\", \"name\", \"CUnit\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsShow\", \"name\", \"CIsShow\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"langg\", \"\", 3, \"value\"]],\n      template: function RequirementManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"label\", 6);\n          i0.ɵɵtext(8, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CBuildCaseID, $event) || (ctx.getListRequirementRequest.CBuildCaseID = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(10, \"nb-option\", 8);\n          i0.ɵɵtext(11, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, RequirementManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 5)(14, \"label\", 10);\n          i0.ɵɵtext(15, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 5)(18, \"label\", 12);\n          i0.ɵɵtext(19, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_20_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CGroupName, $event) || (ctx.getListRequirementRequest.CGroupName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 4)(22, \"div\", 5)(23, \"label\", 14);\n          i0.ɵɵtext(24, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(26, RequirementManagementComponent_nb_option_26_Template, 2, 2, \"nb-option\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 5)(28, \"label\", 16);\n          i0.ɵɵtext(29, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(31, \"nb-option\", 8);\n          i0.ɵɵtext(32, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-option\", 8);\n          i0.ɵɵtext(34, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"nb-option\", 8);\n          i0.ɵɵtext(36, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 5)(38, \"label\", 17);\n          i0.ɵɵtext(39, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsShow, $event) || (ctx.getListRequirementRequest.CIsShow = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(41, \"nb-option\", 8);\n          i0.ɵɵtext(42, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"nb-option\", 8);\n          i0.ɵɵtext(44, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"nb-option\", 8);\n          i0.ɵɵtext(46, \"\\u5426\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"div\", 4);\n          i0.ɵɵelement(48, \"div\", 18);\n          i0.ɵɵelementStart(49, \"div\", 19)(50, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_50_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetSearch());\n          });\n          i0.ɵɵelement(51, \"i\", 21);\n          i0.ɵɵtext(52, \"\\u91CD\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_53_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(54, \"i\", 23);\n          i0.ɵɵtext(55, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(56, RequirementManagementComponent_button_56_Template, 3, 0, \"button\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(57, \"nb-card-body\", 2)(58, \"div\", 3)(59, \"div\", 25)(60, \"table\", 26)(61, \"thead\")(62, \"tr\", 27)(63, \"th\", 28);\n          i0.ɵɵtext(64, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\", 28);\n          i0.ɵɵtext(66, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\", 29);\n          i0.ɵɵtext(68, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"th\", 29);\n          i0.ɵɵtext(70, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"th\", 29);\n          i0.ɵɵtext(72, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"th\", 29);\n          i0.ɵɵtext(74, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"th\", 29);\n          i0.ɵɵtext(76, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"th\", 29);\n          i0.ɵɵtext(78, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"th\", 28);\n          i0.ɵɵtext(80, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(81, \"tbody\");\n          i0.ɵɵtemplate(82, RequirementManagementComponent_tr_82_Template, 22, 15, \"tr\", 30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(83, \"ngx-pagination\", 31);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_83_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_83_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(84, RequirementManagementComponent_ng_template_84_Template, 39, 45, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CBuildCaseID);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CGroupName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsShow);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.MaxLengthValidator, i9.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent, NumberWithCommasPipe, NbTabsetModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVxdWlyZW1lbnQtbWFuYWdlbWVudC9yZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3TEFBd0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "NbTabsetModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "type_r3", "value", "label", "ɵɵlistener", "RequirementManagementComponent_button_56_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r4", "ɵɵnextContext", "dialog_r6", "ɵɵreference", "ɵɵresetView", "add", "ɵɵelement", "RequirementManagementComponent_tr_82_button_20_Template_button_click_0_listener", "_r7", "data_r8", "$implicit", "onEdit", "RequirementManagementComponent_tr_82_button_21_Template_button_click_0_listener", "_r9", "onDelete", "ɵɵtemplate", "RequirementManagementComponent_tr_82_button_20_Template", "RequirementManagementComponent_tr_82_button_21_Template", "ɵɵtextInterpolate", "CRequirement", "CGroupName", "getHouseType", "CHouseType", "ɵɵpureFunction0", "_c0", "CSort", "ɵɵpipeBind1", "CStatus", "getCIsShowText", "CUnitPrice", "isUpdate", "isDelete", "b_r11", "type_r12", "status_r13", "RequirementManagementComponent_ng_template_84_span_2_Template", "RequirementManagementComponent_ng_template_84_span_3_Template", "ɵɵtwoWayListener", "RequirementManagementComponent_ng_template_84_Template_nb_select_selectedChange_9_listener", "$event", "_r10", "ɵɵtwoWayBindingSet", "saveRequirement", "CBuildCaseID", "RequirementManagementComponent_ng_template_84_nb_option_10_Template", "RequirementManagementComponent_ng_template_84_Template_input_ngModelChange_12_listener", "RequirementManagementComponent_ng_template_84_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_ng_template_84_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_ng_template_84_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_84_nb_option_19_Template", "RequirementManagementComponent_ng_template_84_Template_nb_select_selectedChange_21_listener", "RequirementManagementComponent_ng_template_84_nb_option_22_Template", "RequirementManagementComponent_ng_template_84_Template_input_ngModelChange_24_listener", "RequirementManagementComponent_ng_template_84_Template_input_ngModelChange_26_listener", "CUnit", "RequirementManagementComponent_ng_template_84_Template_nb_checkbox_ngModelChange_28_listener", "CIsShow", "RequirementManagementComponent_ng_template_84_Template_textarea_ngModelChange_31_listener", "CRemark", "RequirementManagementComponent_ng_template_84_Template_button_click_35_listener", "ref_r14", "dialogRef", "save", "RequirementManagementComponent_ng_template_84_Template_button_click_37_listener", "close", "isNew", "ɵɵtwoWayProperty", "buildCaseList", "houseType", "statusOptions", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getListRequirementRequest", "getRequirementRequest", "requirementList", "getEnumOptions", "currentBuildCase", "currentTab", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "map", "type", "resetSearch", "length", "setTimeout", "getList", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "errorMessages", "dialog", "open", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "TotalItems", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "filter", "v", "onTabChange", "event", "tabTitle", "addTemplate", "onEditTemplate", "_this2", "saveTemplate", "templateData", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "BuildCaseService", "RequirementService", "i7", "PetternHelper", "i8", "Router", "DestroyRef", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequirementManagementComponent_Template", "rf", "ctx", "RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener", "_r1", "RequirementManagementComponent_nb_option_12_Template", "RequirementManagementComponent_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_Template_input_ngModelChange_20_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_25_listener", "RequirementManagementComponent_nb_option_26_Template", "RequirementManagementComponent_Template_nb_select_ngModelChange_30_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_40_listener", "RequirementManagementComponent_Template_button_click_50_listener", "RequirementManagementComponent_Template_button_click_53_listener", "RequirementManagementComponent_button_56_Template", "RequirementManagementComponent_tr_82_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_83_listener", "RequirementManagementComponent_ng_template_84_Template", "ɵɵtemplateRefExtractor", "isCreate", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "NbSelectComponent", "NbOptionComponent", "NbCheckboxComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    NbTabsetModule\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n  currentTab = 0; // 追蹤當前選中的 tab\r\n\r\n  override ngOnInit(): void { }\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.getListRequirementRequest.CBuildCaseID = -1;\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CGroupName = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    // 根據當前 tab 決定是否需要驗證建案名稱\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面需要驗證建案名稱\r\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    }\r\n\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 根據當前 tab 決定是否需要建案ID\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n      if (this.currentBuildCase != 0) {\r\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    } else {\r\n      // 模板頁面 - 設定建案ID為0\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 載入建案列表後，如果有建案資料，預設選擇第一個\r\n        if (this.buildCaseList.length > 0) {\r\n          // 使用 setTimeout 確保在下一個變更檢測週期中更新\r\n          setTimeout(() => {\r\n            this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n            // 只在初始載入時執行查詢\r\n            this.getList();\r\n          }, 0);\r\n        } else {\r\n          // 如果沒有建案資料，直接執行查詢\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      // 建案頁面的邏輯保持不變\r\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n      }\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.requirementList = res.Entries;\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  } getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            // TODO: 等後端API更新後啟用這行\r\n            this.saveRequirement.CIsShow = (res.Entries as any).CIsShow || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  // Tab 切換事件處理\r\n  onTabChange(event: any) {\r\n    // 根據 tabTitle 來判斷當前頁面\r\n    if (event.tabTitle === '共用') {\r\n      this.currentTab = 1;\r\n    } else {\r\n      this.currentTab = 0;\r\n    }\r\n\r\n    // 當切換到模板頁面時，重置建案選擇並設為不可選\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n      this.getList(); // 切換 tab 時重新載入列表\r\n    } else {\r\n      // 切換回建案頁面時，如果沒有選擇建案且有建案資料，預設選擇第一個\r\n      if (this.getListRequirementRequest.CBuildCaseID === 0 || this.getListRequirementRequest.CBuildCaseID === -1) {\r\n        if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n          setTimeout(() => {\r\n            this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n            this.getList(); // 切換 tab 時重新載入列表\r\n          }, 0);\r\n        } else {\r\n          this.getListRequirementRequest.CBuildCaseID = -1;\r\n          this.getList(); // 切換 tab 時重新載入列表\r\n        }\r\n      } else {\r\n        this.getList(); // 切換 tab 時重新載入列表\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增模板\r\n  addTemplate(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    // 模板設定建案ID為0\r\n    this.saveRequirement.CBuildCaseID = 0;\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 編輯模板\r\n  async onEditTemplate(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get template data\", error);\r\n    }\r\n  }\r\n\r\n  // 保存模板\r\n  saveTemplate(ref: any) {\r\n    // 模板驗證（不包含建案名稱）\r\n    this.valid.clear();\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 確保模板建案ID為0\r\n    const templateData = { ...this.saveRequirement };\r\n    templateData.CBuildCaseID = 0;\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: templateData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"buildCase\" class=\"label mr-2\">建案</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CBuildCaseID\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"requirement\" class=\"label mr-2\">工程項目</label>\r\n          <input type=\"text\" nbInput id=\"requirement\" name=\"requirement\" placeholder=\"工程項目\"\r\n            [(ngModel)]=\"getListRequirementRequest.CRequirement\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"groupName\" class=\"label mr-2\">群組類別</label>\r\n          <input type=\"text\" nbInput id=\"groupName\" name=\"groupName\" placeholder=\"群組類別\"\r\n            [(ngModel)]=\"getListRequirementRequest.CGroupName\">\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"houseType\" class=\"label mr-2\">類型</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CHouseType\" class=\"col-9\" multiple>\r\n            <nb-option *ngFor=\"let type of houseType\" [value]=\"type.value\">\r\n              {{ type.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CStatus\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isShow\" class=\"label mr-2\">客變需求顯示</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsShow\" class=\"col-9\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\"></div>\r\n        <div class=\"form-group col-12 col-md-6 text-right\">\r\n          <button class=\"btn btn-secondary mr-2\" (click)=\"resetSearch()\"><i class=\"fas fa-undo mr-1\"></i>重置</button>\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n              <th scope=\"col\" class=\"col-2\">建案名稱</th>\r\n              <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n              <th scope=\"col\" class=\"col-1\">群組類別</th>\r\n              <th scope=\"col\" class=\"col-1\">類型</th>\r\n              <th scope=\"col\" class=\"col-1\">排序</th>\r\n              <th scope=\"col\" class=\"col-1\">狀態</th>\r\n              <th scope=\"col\" class=\"col-1\">客變需求顯示</th>\r\n              <th scope=\"col\" class=\"col-1\">單價</th>\r\n              <th scope=\"col\" class=\"col-2\">操作功能</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n              <td class=\"col-2\">{{ data.CBuildCaseName }}</td>\r\n              <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n              <td class=\"col-1\">{{ data.CGroupName }}</td>\r\n              <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n              <td class=\"col-1\">{{ data.CSort }}</td>\r\n              <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n              <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n              <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n              <td class=\"col-2\">\r\n                <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                  (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                  (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n        (PageChange)=\"getList()\">\r\n      </ngx-pagination>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增</span>\r\n      <span *ngIf=\"isNew===false\">編輯</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'建案名稱'\" [labelFor]=\"'CBuildCaseID'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CBuildCaseID\" name=\"CBuildCaseID\"\r\n                [(selected)]=\"saveRequirement.CBuildCaseID\">\r\n                <nb-option langg *ngFor=\"let b of buildCaseList\" [value]=\"b.cID\"> {{b.CBuildCaseName}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group> <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group> <app-form-group [label]=\"'群組類別'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組類別\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple>\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'客變需求顯示'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"false\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在客變需求\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AAEA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/I,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;ICRrDC,EAAA,CAAAC,cAAA,mBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;IAkBAT,EAAA,CAAAC,cAAA,mBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAC,KAAA,CAAoB;IAC5DX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAE,KAAA,MACF;;;;;;IAyBFZ,EAAA,CAAAC,cAAA,iBAA4E;IAAvCD,EAAA,CAAAa,UAAA,mBAAAC,0EAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAK,GAAA,CAAAH,SAAA,CAAW;IAAA,EAAC;IAAkBnB,EAAA,CAAAuB,SAAA,YAC3C;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAkCtCH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAa,UAAA,mBAAAW,gFAAA;MAAAxB,EAAA,CAAAe,aAAA,CAAAU,GAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAkB,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAW,MAAA,CAAAF,OAAA,EAAAP,SAAA,CAAmB;IAAA,EAAC;IAACnB,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAa,UAAA,mBAAAgB,gFAAA;MAAA7B,EAAA,CAAAe,aAAA,CAAAe,GAAA;MAAA,MAAAJ,OAAA,GAAA1B,EAAA,CAAAkB,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAc,QAAA,CAAAL,OAAA,CAAc;IAAA,EAAC;IAAC1B,EAAA,CAAAuB,SAAA,YAAqC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAZ7EH,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAgC,UAAA,KAAAC,uDAAA,qBACgC,KAAAC,uDAAA,qBAEL;IAE/BlC,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAdeH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAjB,cAAA,CAAyB;IACzBT,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAU,YAAA,CAAuB;IACvBpC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAW,UAAA,CAAqB;IACrBrC,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAmC,iBAAA,CAAAlB,MAAA,CAAAqB,YAAA,CAAAZ,OAAA,CAAAa,UAAA,IAAAvC,EAAA,CAAAwC,eAAA,KAAAC,GAAA,GAAyC;IACzCzC,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAgB,KAAA,CAAgB;IAChB1C,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAA2C,WAAA,SAAAjB,OAAA,CAAAkB,OAAA,EAAkC;IAClC5C,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAmC,iBAAA,CAAAlB,MAAA,CAAA4B,cAAA,CAAAnB,OAAA,EAA0B;IAC1B1B,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAA2C,WAAA,SAAAjB,OAAA,CAAAoB,UAAA,OAAkD;IAEzD9C,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA8B,QAAA,CAAc;IAEd/C,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+B,QAAA,CAAc;;;;;IAkBjChD,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACpCH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS3BH,EAAA,CAAAC,cAAA,oBAAiE;IAACD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAjDH,EAAA,CAAAI,UAAA,UAAA6C,KAAA,CAAA3C,GAAA,CAAe;IAAEN,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAQ,kBAAA,MAAAyC,KAAA,CAAAxC,cAAA,KAAoB;;;;;IAgBtFT,EAAA,CAAAC,cAAA,oBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAA8C,QAAA,CAAAvC,KAAA,CAAoB;IAAEX,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAA0C,QAAA,CAAAtC,KAAA,KAAc;;;;;IAMpFZ,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAA+C,UAAA,CAAAxC,KAAA,CAAsB;IAC1EX,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAA2C,UAAA,CAAAvC,KAAA,KAAgB;;;;;;IAlC9BZ,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAAgC,UAAA,IAAAoB,6DAAA,mBAA2B,IAAAC,6DAAA,mBACC;IAC9BrD,EAAA,CAAAG,YAAA,EAAiB;IAMPH,EALV,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX,yBACkE,oBAEjC;IAA5CD,EAAA,CAAAsD,gBAAA,4BAAAC,2FAAAC,MAAA;MAAAxD,EAAA,CAAAe,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAA0D,kBAAA,CAAAzC,MAAA,CAAA0C,eAAA,CAAAC,YAAA,EAAAJ,MAAA,MAAAvC,MAAA,CAAA0C,eAAA,CAAAC,YAAA,GAAAJ,MAAA;MAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;IAAA,EAA2C;IAC3CxD,EAAA,CAAAgC,UAAA,KAAA6B,mEAAA,wBAAiE;IAErE7D,EADE,CAAAG,YAAA,EAAY,EACG;IACfH,EADgB,CAAAC,cAAA,0BAAiF,iBAErC;IAA1DD,EAAA,CAAAsD,gBAAA,2BAAAQ,uFAAAN,MAAA;MAAAxD,EAAA,CAAAe,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAA0D,kBAAA,CAAAzC,MAAA,CAAA0C,eAAA,CAAAvB,YAAA,EAAAoB,MAAA,MAAAvC,MAAA,CAAA0C,eAAA,CAAAvB,YAAA,GAAAoB,MAAA;MAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;IAAA,EAA0C;IAC9CxD,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IACfH,EADgB,CAAAC,cAAA,0BAAgF,iBAEtC;IAAxDD,EAAA,CAAAsD,gBAAA,2BAAAS,uFAAAP,MAAA;MAAAxD,EAAA,CAAAe,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAA0D,kBAAA,CAAAzC,MAAA,CAAA0C,eAAA,CAAAtB,UAAA,EAAAmB,MAAA,MAAAvC,MAAA,CAAA0C,eAAA,CAAAtB,UAAA,GAAAmB,MAAA;MAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;IAAA,EAAwC;IAC5CxD,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAsD,gBAAA,2BAAAU,uFAAAR,MAAA;MAAAxD,EAAA,CAAAe,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAA0D,kBAAA,CAAAzC,MAAA,CAAA0C,eAAA,CAAAjB,KAAA,EAAAc,MAAA,MAAAvC,MAAA,CAAA0C,eAAA,CAAAjB,KAAA,GAAAc,MAAA;MAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;IAAA,EAAmC;IACvCxD,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEtB;IAAnDD,EAAA,CAAAsD,gBAAA,4BAAAW,4FAAAT,MAAA;MAAAxD,EAAA,CAAAe,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAA0D,kBAAA,CAAAzC,MAAA,CAAA0C,eAAA,CAAApB,UAAA,EAAAiB,MAAA,MAAAvC,MAAA,CAAA0C,eAAA,CAAApB,UAAA,GAAAiB,MAAA;MAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;IAAA,EAAyC;IACzCxD,EAAA,CAAAgC,UAAA,KAAAkC,mEAAA,wBAAqE;IAEzElE,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAsD,gBAAA,4BAAAa,4FAAAX,MAAA;MAAAxD,EAAA,CAAAe,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAA0D,kBAAA,CAAAzC,MAAA,CAAA0C,eAAA,CAAAf,OAAA,EAAAY,MAAA,MAAAvC,MAAA,CAAA0C,eAAA,CAAAf,OAAA,GAAAY,MAAA;MAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;IAAA,EAAsC;IACtCxD,EAAA,CAAAgC,UAAA,KAAAoC,mEAAA,wBAA6E;IAGjFpE,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAEhC;IAAzCD,EAAA,CAAAsD,gBAAA,2BAAAe,uFAAAb,MAAA;MAAAxD,EAAA,CAAAe,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAA0D,kBAAA,CAAAzC,MAAA,CAAA0C,eAAA,CAAAb,UAAA,EAAAU,MAAA,MAAAvC,MAAA,CAAA0C,eAAA,CAAAb,UAAA,GAAAU,MAAA;MAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;IAAA,EAAwC;IAC5CxD,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAsD,gBAAA,2BAAAgB,uFAAAd,MAAA;MAAAxD,EAAA,CAAAe,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAA0D,kBAAA,CAAAzC,MAAA,CAAA0C,eAAA,CAAAY,KAAA,EAAAf,MAAA,MAAAvC,MAAA,CAAA0C,eAAA,CAAAY,KAAA,GAAAf,MAAA;MAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;IAAA,EAAmC;IACvCxD,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA+E,uBACsB;IAA1DD,EAAA,CAAAsD,gBAAA,2BAAAkB,6FAAAhB,MAAA;MAAAxD,EAAA,CAAAe,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAA0D,kBAAA,CAAAzC,MAAA,CAAA0C,eAAA,CAAAc,OAAA,EAAAjB,MAAA,MAAAvC,MAAA,CAAA0C,eAAA,CAAAc,OAAA,GAAAjB,MAAA;MAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;IAAA,EAAqC;IAC5ExD,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,oBAEV;IAA/DD,EAAA,CAAAsD,gBAAA,2BAAAoB,0FAAAlB,MAAA;MAAAxD,EAAA,CAAAe,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAA0D,kBAAA,CAAAzC,MAAA,CAAA0C,eAAA,CAAAgB,OAAA,EAAAnB,MAAA,MAAAvC,MAAA,CAAA0C,eAAA,CAAAgB,OAAA,GAAAnB,MAAA;MAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;IAAA,EAAqC;IAKjDxD,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBAC2B;IAApBD,EAAA,CAAAa,UAAA,mBAAA+D,gFAAA;MAAA,MAAAC,OAAA,GAAA7E,EAAA,CAAAe,aAAA,CAAA0C,IAAA,EAAAqB,SAAA;MAAA,MAAA7D,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAA8D,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAAC7E,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAAa,UAAA,mBAAAmE,gFAAA;MAAA,MAAAH,OAAA,GAAA7E,EAAA,CAAAe,aAAA,CAAA0C,IAAA,EAAAqB,SAAA;MAAA,OAAA9E,EAAA,CAAAqB,WAAA,CAASwD,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAACjF,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IAjECH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAiE,KAAA,UAAkB;IAClBlF,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAiE,KAAA,WAAmB;IAMJlF,EAAA,CAAAO,SAAA,GAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAAmF,gBAAA,aAAAlE,MAAA,CAAA0C,eAAA,CAAAC,YAAA,CAA2C;IACZ5D,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAmE,aAAA,CAAgB;IAEjBpF,EAAA,CAAAO,SAAA,EAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE9FJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAmF,gBAAA,YAAAlE,MAAA,CAAA0C,eAAA,CAAAvB,YAAA,CAA0C;IACZpC,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE7FJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAmF,gBAAA,YAAAlE,MAAA,CAAA0C,eAAA,CAAAtB,UAAA,CAAwC;IAE5BrC,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAmF,gBAAA,YAAAlE,MAAA,CAAA0C,eAAA,CAAAjB,KAAA,CAAmC;IAEvB1C,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAmF,gBAAA,aAAAlE,MAAA,CAAA0C,eAAA,CAAApB,UAAA,CAAyC;IACPvC,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAoE,SAAA,CAAY;IAGlCrF,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAmF,gBAAA,aAAAlE,MAAA,CAAA0C,eAAA,CAAAf,OAAA,CAAsC;IACF5C,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAqE,aAAA,CAAgB;IAIxCtF,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAmF,gBAAA,YAAAlE,MAAA,CAAA0C,eAAA,CAAAb,UAAA,CAAwC;IAE5B9C,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAmF,gBAAA,YAAAlE,MAAA,CAAA0C,eAAA,CAAAY,KAAA,CAAmC;IAEvBvE,EAAA,CAAAO,SAAA,EAAkB;IAAwBP,EAA1C,CAAAI,UAAA,iDAAkB,uBAAuB,qBAAqB;IACnCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAmF,gBAAA,YAAAlE,MAAA,CAAA0C,eAAA,CAAAc,OAAA,CAAqC;IAIhEzE,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAmF,gBAAA,YAAAlE,MAAA,CAAA0C,eAAA,CAAAgB,OAAA,CAAqC;;;AD1HrD,OAAM,MAAOY,8BAA+B,SAAQlG,aAAa;EAC/DmG,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAMpB;IACA,KAAAC,yBAAyB,GAAG,EAA8D;IAC1F,KAAAC,qBAAqB,GAA8B,EAAE;IACrD;IACA,KAAAhB,aAAa,GAA8B,EAAE;IAC7C,KAAAiB,eAAe,GAAqB,EAAE;IACtC,KAAA1C,eAAe,GAAgD;MAAEpB,UAAU,EAAE;IAAE,CAAE;IAEjF,KAAA+C,aAAa,GAAG,CACd;MAAE3E,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAyE,SAAS,GAAG,IAAI,CAACK,UAAU,CAACY,cAAc,CAACvG,aAAa,CAAC;IACzD,KAAAmF,KAAK,GAAG,KAAK;IACb,KAAAqB,gBAAgB,GAAG,CAAC;IACpB,KAAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAlBd,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAkBSC,QAAQA,CAAA,GAAW;EAC5B;EACAF,oBAAoBA,CAAA;IAClB,IAAI,CAACN,yBAAyB,CAACvC,YAAY,GAAG,CAAC,CAAC;IAChD,IAAI,CAACuC,yBAAyB,CAACvD,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACuD,yBAAyB,CAAC1B,OAAO,GAAG,IAAI;IAC7C,IAAI,CAAC0B,yBAAyB,CAAC/D,YAAY,GAAG,EAAE;IAChD,IAAI,CAAC+D,yBAAyB,CAAC9D,UAAU,GAAG,EAAE;IAC9C;IACA,IAAI,CAAC8D,yBAAyB,CAAC5D,UAAU,GAAG,IAAI,CAAC8C,SAAS,CAACuB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAClG,KAAK,CAAC;EACpF;EAEA;EACAmG,WAAWA,CAAA;IACT,IAAI,CAACL,oBAAoB,EAAE;IAC3B;IACA,IAAI,IAAI,CAACrB,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2B,MAAM,GAAG,CAAC,EAAE;MACvDC,UAAU,CAAC,MAAK;QACd,IAAI,CAACb,yBAAyB,CAACvC,YAAY,GAAG,IAAI,CAACwB,aAAa,CAAC,CAAC,CAAC,CAAC9E,GAAG;QACvE,IAAI,CAAC2G,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEA3E,YAAYA,CAAC4E,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAACnC,SAAS,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/G,KAAK,IAAI4G,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC5G,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOyG,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAChC,KAAK,CAACiC,KAAK,EAAE;IAElB;IACA,IAAI,IAAI,CAACtB,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,CAACX,KAAK,CAACkC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACpE,eAAe,CAACC,YAAY,CAAC;IAClE;IAEA,IAAI,CAACiC,KAAK,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpE,eAAe,CAACvB,YAAY,CAAC;IAC9D,IAAI,CAACyD,KAAK,CAACkC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACpE,eAAe,CAACpB,UAAU,CAAC;IAC7D,IAAI,CAACsD,KAAK,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpE,eAAe,CAACjB,KAAK,CAAC;IACvD,IAAI,CAACmD,KAAK,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpE,eAAe,CAACf,OAAO,CAAC;IACzD,IAAI,CAACiD,KAAK,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpE,eAAe,CAACb,UAAU,CAAC;IAC5D,IAAI,CAAC+C,KAAK,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpE,eAAe,CAACY,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACZ,eAAe,CAACtB,UAAU,IAAI,IAAI,CAACsB,eAAe,CAACtB,UAAU,CAAC0E,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAAClB,KAAK,CAACmC,aAAa,CAACL,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAChE,eAAe,CAACgB,OAAO,IAAI,IAAI,CAAChB,eAAe,CAACgB,OAAO,CAACoC,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAAClB,KAAK,CAACmC,aAAa,CAACL,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEArG,GAAGA,CAAC2G,MAAwB;IAC1B,IAAI,CAAC/C,KAAK,GAAG,IAAI;IACjB,IAAI,CAACvB,eAAe,GAAG;MAAEpB,UAAU,EAAE,EAAE;MAAEkC,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACd,eAAe,CAACf,OAAO,GAAG,CAAC;IAChC,IAAI,CAACe,eAAe,CAACb,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAAC0D,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,IAAI,CAACD,gBAAgB,IAAI,CAAC,EAAE;QAC9B,IAAI,CAAC5C,eAAe,CAACC,YAAY,GAAG,IAAI,CAAC2C,gBAAgB;MAC3D,CAAC,MAAM,IAAI,IAAI,CAACnB,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2B,MAAM,GAAG,CAAC,EAAE;QAC9D,IAAI,CAACpD,eAAe,CAACC,YAAY,GAAG,IAAI,CAACwB,aAAa,CAAC,CAAC,CAAC,CAAC9E,GAAG;MAC/D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACqD,eAAe,CAACC,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAAC+B,aAAa,CAACuC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEMrG,MAAMA,CAACuG,IAAoB,EAAEF,MAAwB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAAChC,qBAAqB,CAACkC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAAClD,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMkD,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAACzC,aAAa,CAACuC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAzD,IAAIA,CAAC4D,GAAQ;IACX,IAAI,CAACd,UAAU,EAAE;IACjB,IAAI,IAAI,CAAChC,KAAK,CAACmC,aAAa,CAACjB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACnB,OAAO,CAACgD,aAAa,CAAC,IAAI,CAAC/C,KAAK,CAACmC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAI,IAAI,CAACxB,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC7C,eAAe,CAACC,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAACmC,kBAAkB,CAAC8C,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAACnF;KACZ,CAAC,CAACoF,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACrD,OAAO,CAACsD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACjC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACrB,OAAO,CAACuD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAAC1D,KAAK,EAAE;EACb;EAEAlD,QAAQA,CAACoG,IAAoB;IAC3B,IAAI,CAACxE,eAAe,CAAC2E,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAACpD,KAAK,GAAG,KAAK;IAClB,IAAImE,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACxD,kBAAkB,CAACyD,iCAAiC,CAAC;MACxDV,IAAI,EAAE;QACJR,cAAc,EAAE,IAAI,CAAC3E,eAAe,CAAC2E;;KAExC,CAAC,CAACS,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACpD,OAAO,CAACsD,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACjC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAP,gBAAgBA,CAAA;IACd,IAAI,CAACZ,gBAAgB,CAAC2D,qCAAqC,CAAC;MAAEX,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEY,IAAI,CAACpK,kBAAkB,CAAC,IAAI,CAAC4G,UAAU,CAAC,CAAC,CAAC6C,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAC5D,aAAa,GAAG4D,GAAG,CAACW,OAAQ;MACjC;MACA,IAAI,IAAI,CAACvE,aAAa,CAAC2B,MAAM,GAAG,CAAC,EAAE;QACjC;QACAC,UAAU,CAAC,MAAK;UACd,IAAI,CAACb,yBAAyB,CAACvC,YAAY,GAAG,IAAI,CAACwB,aAAa,CAAC,CAAC,CAAC,CAAC9E,GAAG;UACvE;UACA,IAAI,CAAC2G,OAAO,EAAE;QAChB,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,MAAM;QACL;QACA,IAAI,CAACA,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAACd,yBAAyB,CAACyD,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAAC1D,yBAAyB,CAAC2D,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAAC1D,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAAC2D,YAAY,GAAG,CAAC;IACrB;IACA,IAAI,IAAI,CAACxD,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACL,yBAAyB,CAACvC,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACuC,yBAAyB,CAACvC,YAAY,IAAI,IAAI,CAACuC,yBAAyB,CAACvC,YAAY,IAAI,CAAC,EAAE;QACnG,IAAI,CAAC2C,gBAAgB,GAAG,IAAI,CAACJ,yBAAyB,CAACvC,YAAY;MACrE;IACF;IAEA,IAAI,CAACmC,kBAAkB,CAACkE,8BAA8B,CAAC;MAAEnB,IAAI,EAAE,IAAI,CAAC3C;IAAyB,CAAE,CAAC,CAC7FuD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACtD,eAAe,GAAG2C,GAAG,CAACW,OAAO;UAClC,IAAI,CAACK,YAAY,GAAGhB,GAAG,CAACkB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAAE3B,OAAOA,CAAA;IACP,IAAI,CAACxC,kBAAkB,CAACoE,8BAA8B,CAAC;MAAErB,IAAI,EAAE,IAAI,CAAC1C;IAAqB,CAAE,CAAC,CACzFsD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAAChG,eAAe,GAAG;YAAEpB,UAAU,EAAE,EAAE;YAAEkC,OAAO,EAAE;UAAK,CAAE;UACzD,IAAI,CAACd,eAAe,CAACC,YAAY,GAAGoF,GAAG,CAACW,OAAO,CAAC/F,YAAY;UAC5D,IAAI,CAACD,eAAe,CAACtB,UAAU,GAAG2G,GAAG,CAACW,OAAO,CAACtH,UAAU;UACxD,IAAI,CAACsB,eAAe,CAACpB,UAAU,GAAGyG,GAAG,CAACW,OAAO,CAACpH,UAAU,GAAI4E,KAAK,CAACC,OAAO,CAAC4B,GAAG,CAACW,OAAO,CAACpH,UAAU,CAAC,GAAGyG,GAAG,CAACW,OAAO,CAACpH,UAAU,GAAG,CAACyG,GAAG,CAACW,OAAO,CAACpH,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACoB,eAAe,CAACgB,OAAO,GAAGqE,GAAG,CAACW,OAAO,CAAChF,OAAO;UAClD,IAAI,CAAChB,eAAe,CAACvB,YAAY,GAAG4G,GAAG,CAACW,OAAO,CAACvH,YAAY;UAC5D,IAAI,CAACuB,eAAe,CAAC2E,cAAc,GAAGU,GAAG,CAACW,OAAO,CAACrB,cAAc;UAChE,IAAI,CAAC3E,eAAe,CAACjB,KAAK,GAAGsG,GAAG,CAACW,OAAO,CAACjH,KAAK;UAC9C,IAAI,CAACiB,eAAe,CAACf,OAAO,GAAGoG,GAAG,CAACW,OAAO,CAAC/G,OAAO;UAClD,IAAI,CAACe,eAAe,CAACb,UAAU,GAAGkG,GAAG,CAACW,OAAO,CAAC7G,UAAU;UACxD,IAAI,CAACa,eAAe,CAACY,KAAK,GAAGyE,GAAG,CAACW,OAAO,CAACpF,KAAK;UAC9C;UACA,IAAI,CAACZ,eAAe,CAACc,OAAO,GAAIuE,GAAG,CAACW,OAAe,CAAClF,OAAO,IAAI,KAAK;QACtE;MACF;IACF,CAAC,CAAC;EACN;EAEA2F,iBAAiBA,CAACzJ,KAAa,EAAE0J,OAAY;IAC3C5B,OAAO,CAACC,GAAG,CAAC2B,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAAC1G,eAAe,CAACpB,UAAU,EAAE+H,QAAQ,CAAC3J,KAAK,CAAC,EAAE;QACrD,IAAI,CAACgD,eAAe,CAACpB,UAAU,EAAEoF,IAAI,CAAChH,KAAK,CAAC;MAC9C;MACA8H,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC/E,eAAe,CAACpB,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACoB,eAAe,CAACpB,UAAU,GAAG,IAAI,CAACoB,eAAe,CAACpB,UAAU,EAAEgI,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK7J,KAAK,CAAC;IAC7F;EACF;EAEAkC,cAAcA,CAACsF,IAAS;IACtB,OAAOA,IAAI,CAAC1D,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAEA;EACAgG,WAAWA,CAACC,KAAU;IACpB;IACA,IAAIA,KAAK,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACnE,UAAU,GAAG,CAAC;IACrB,CAAC,MAAM;MACL,IAAI,CAACA,UAAU,GAAG,CAAC;IACrB;IAEA;IACA,IAAI,IAAI,CAACA,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACL,yBAAyB,CAACvC,YAAY,GAAG,CAAC;MAC/C,IAAI,CAACqD,OAAO,EAAE,CAAC,CAAC;IAClB,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACd,yBAAyB,CAACvC,YAAY,KAAK,CAAC,IAAI,IAAI,CAACuC,yBAAyB,CAACvC,YAAY,KAAK,CAAC,CAAC,EAAE;QAC3G,IAAI,IAAI,CAACwB,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2B,MAAM,GAAG,CAAC,EAAE;UACvDC,UAAU,CAAC,MAAK;YACd,IAAI,CAACb,yBAAyB,CAACvC,YAAY,GAAG,IAAI,CAACwB,aAAa,CAAC,CAAC,CAAC,CAAC9E,GAAG;YACvE,IAAI,CAAC2G,OAAO,EAAE,CAAC,CAAC;UAClB,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,MAAM;UACL,IAAI,CAACd,yBAAyB,CAACvC,YAAY,GAAG,CAAC,CAAC;UAChD,IAAI,CAACqD,OAAO,EAAE,CAAC,CAAC;QAClB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,OAAO,EAAE,CAAC,CAAC;MAClB;IACF;EACF;EAEA;EACA2D,WAAWA,CAAC3C,MAAwB;IAClC,IAAI,CAAC/C,KAAK,GAAG,IAAI;IACjB,IAAI,CAACvB,eAAe,GAAG;MAAEpB,UAAU,EAAE,EAAE;MAAEkC,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACd,eAAe,CAACf,OAAO,GAAG,CAAC;IAChC,IAAI,CAACe,eAAe,CAACb,UAAU,GAAG,CAAC;IACnC;IACA,IAAI,CAACa,eAAe,CAACC,YAAY,GAAG,CAAC;IACrC,IAAI,CAAC+B,aAAa,CAACuC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACM4C,cAAcA,CAAC1C,IAAoB,EAAEF,MAAwB;IAAA,IAAA6C,MAAA;IAAA,OAAAzC,iBAAA;MACjEyC,MAAI,CAAC1E,qBAAqB,CAACkC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEwC,MAAI,CAAC5F,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM4F,MAAI,CAACvC,OAAO,EAAE;QACpBuC,MAAI,CAACnF,aAAa,CAACuC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA;EACAuC,YAAYA,CAACpC,GAAQ;IACnB;IACA,IAAI,CAAC9C,KAAK,CAACiC,KAAK,EAAE;IAClB,IAAI,CAACjC,KAAK,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpE,eAAe,CAACvB,YAAY,CAAC;IAC9D,IAAI,CAACyD,KAAK,CAACkC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACpE,eAAe,CAACpB,UAAU,CAAC;IAC7D,IAAI,CAACsD,KAAK,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpE,eAAe,CAACjB,KAAK,CAAC;IACvD,IAAI,CAACmD,KAAK,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpE,eAAe,CAACf,OAAO,CAAC;IACzD,IAAI,CAACiD,KAAK,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpE,eAAe,CAACb,UAAU,CAAC;IAC5D,IAAI,CAAC+C,KAAK,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpE,eAAe,CAACY,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACZ,eAAe,CAACtB,UAAU,IAAI,IAAI,CAACsB,eAAe,CAACtB,UAAU,CAAC0E,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAAClB,KAAK,CAACmC,aAAa,CAACL,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAChE,eAAe,CAACgB,OAAO,IAAI,IAAI,CAAChB,eAAe,CAACgB,OAAO,CAACoC,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAAClB,KAAK,CAACmC,aAAa,CAACL,IAAI,CAAC,kBAAkB,CAAC;IACnD;IAEA,IAAI,IAAI,CAAC9B,KAAK,CAACmC,aAAa,CAACjB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACnB,OAAO,CAACgD,aAAa,CAAC,IAAI,CAAC/C,KAAK,CAACmC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMgD,YAAY,GAAG;MAAE,GAAG,IAAI,CAACrH;IAAe,CAAE;IAChDqH,YAAY,CAACpH,YAAY,GAAG,CAAC;IAE7B,IAAI,CAACmC,kBAAkB,CAAC8C,+BAA+B,CAAC;MACtDC,IAAI,EAAEkC;KACP,CAAC,CAACjC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACrD,OAAO,CAACsD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACjC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACrB,OAAO,CAACuD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAAC1D,KAAK,EAAE;EACb;;;uCA3WWM,8BAA8B,EAAAvF,EAAA,CAAAiL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnL,EAAA,CAAAiL,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAArL,EAAA,CAAAiL,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAvL,EAAA,CAAAiL,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAzL,EAAA,CAAAiL,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA3L,EAAA,CAAAiL,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA7L,EAAA,CAAAiL,iBAAA,CAAAW,EAAA,CAAAE,kBAAA,GAAA9L,EAAA,CAAAiL,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAAhM,EAAA,CAAAiL,iBAAA,CAAAgB,EAAA,CAAAC,MAAA,GAAAlM,EAAA,CAAAiL,iBAAA,CAAAjL,EAAA,CAAAmM,UAAA;IAAA;EAAA;;;YAA9B5G,8BAA8B;MAAA6G,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAtM,EAAA,CAAAuM,0BAAA,EAAAvM,EAAA,CAAAwM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCzCzC9M,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAuB,SAAA,qBAAiC;UACnCvB,EAAA,CAAAG,YAAA,EAAiB;UAKTH,EAJR,CAAAC,cAAA,sBAA+B,aACT,aACD,aACyB,eACI;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,mBAA8E;UAAnED,EAAA,CAAAsD,gBAAA,2BAAA0J,2EAAAxJ,MAAA;YAAAxD,EAAA,CAAAe,aAAA,CAAAkM,GAAA;YAAAjN,EAAA,CAAA0D,kBAAA,CAAAqJ,GAAA,CAAA5G,yBAAA,CAAAvC,YAAA,EAAAJ,MAAA,MAAAuJ,GAAA,CAAA5G,yBAAA,CAAAvC,YAAA,GAAAJ,MAAA;YAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;UAAA,EAAoD;UAC7DxD,EAAA,CAAAC,cAAA,oBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAgC,UAAA,KAAAkL,oDAAA,uBAAiE;UAIrElN,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACM;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,iBACuD;UAArDD,EAAA,CAAAsD,gBAAA,2BAAA6J,wEAAA3J,MAAA;YAAAxD,EAAA,CAAAe,aAAA,CAAAkM,GAAA;YAAAjN,EAAA,CAAA0D,kBAAA,CAAAqJ,GAAA,CAAA5G,yBAAA,CAAA/D,YAAA,EAAAoB,MAAA,MAAAuJ,GAAA,CAAA5G,yBAAA,CAAA/D,YAAA,GAAAoB,MAAA;YAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;UAAA,EAAoD;UACxDxD,EAFE,CAAAG,YAAA,EACuD,EACnD;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACI;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,iBACqD;UAAnDD,EAAA,CAAAsD,gBAAA,2BAAA8J,wEAAA5J,MAAA;YAAAxD,EAAA,CAAAe,aAAA,CAAAkM,GAAA;YAAAjN,EAAA,CAAA0D,kBAAA,CAAAqJ,GAAA,CAAA5G,yBAAA,CAAA9D,UAAA,EAAAmB,MAAA,MAAAuJ,GAAA,CAAA5G,yBAAA,CAAA9D,UAAA,GAAAmB,MAAA;YAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;UAAA,EAAkD;UAExDxD,EAHI,CAAAG,YAAA,EACqD,EACjD,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBAAqF;UAA1ED,EAAA,CAAAsD,gBAAA,2BAAA+J,4EAAA7J,MAAA;YAAAxD,EAAA,CAAAe,aAAA,CAAAkM,GAAA;YAAAjN,EAAA,CAAA0D,kBAAA,CAAAqJ,GAAA,CAAA5G,yBAAA,CAAA5D,UAAA,EAAAiB,MAAA,MAAAuJ,GAAA,CAAA5G,yBAAA,CAAA5D,UAAA,GAAAiB,MAAA;YAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;UAAA,EAAkD;UAC3DxD,EAAA,CAAAgC,UAAA,KAAAsL,oDAAA,uBAA+D;UAInEtN,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,oBAAyE;UAA9DD,EAAA,CAAAsD,gBAAA,2BAAAiK,4EAAA/J,MAAA;YAAAxD,EAAA,CAAAe,aAAA,CAAAkM,GAAA;YAAAjN,EAAA,CAAA0D,kBAAA,CAAAqJ,GAAA,CAAA5G,yBAAA,CAAAvD,OAAA,EAAAY,MAAA,MAAAuJ,GAAA,CAAA5G,yBAAA,CAAAvD,OAAA,GAAAY,MAAA;YAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;UAAA,EAA+C;UACxDxD,EAAA,CAAAC,cAAA,oBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,oBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,oBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAE7BF,EAF6B,CAAAG,YAAA,EAAY,EAC3B,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,oBAAyE;UAA9DD,EAAA,CAAAsD,gBAAA,2BAAAkK,4EAAAhK,MAAA;YAAAxD,EAAA,CAAAe,aAAA,CAAAkM,GAAA;YAAAjN,EAAA,CAAA0D,kBAAA,CAAAqJ,GAAA,CAAA5G,yBAAA,CAAA1B,OAAA,EAAAjB,MAAA,MAAAuJ,GAAA,CAAA5G,yBAAA,CAAA1B,OAAA,GAAAjB,MAAA;YAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;UAAA,EAA+C;UACxDxD,EAAA,CAAAC,cAAA,oBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,oBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,oBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAGlCF,EAHkC,CAAAG,YAAA,EAAY,EAC9B,EACR,EACF;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACfD,EAAA,CAAAuB,SAAA,eAA4B;UAE1BvB,EADF,CAAAC,cAAA,eAAmD,kBACc;UAAxBD,EAAA,CAAAa,UAAA,mBAAA4M,iEAAA;YAAAzN,EAAA,CAAAe,aAAA,CAAAkM,GAAA;YAAA,OAAAjN,EAAA,CAAAqB,WAAA,CAAS0L,GAAA,CAAAjG,WAAA,EAAa;UAAA,EAAC;UAAC9G,EAAA,CAAAuB,SAAA,aAAgC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1GH,EAAA,CAAAC,cAAA,kBAAsD;UAApBD,EAAA,CAAAa,UAAA,mBAAA6M,iEAAA;YAAA1N,EAAA,CAAAe,aAAA,CAAAkM,GAAA;YAAA,OAAAjN,EAAA,CAAAqB,WAAA,CAAS0L,GAAA,CAAA9F,OAAA,EAAS;UAAA,EAAC;UAACjH,EAAA,CAAAuB,SAAA,aAAkC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnGH,EAAA,CAAAgC,UAAA,KAAA2L,iDAAA,qBAA4E;UAKpF3N,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;UAQHH,EANZ,CAAAC,cAAA,uBAA+B,cACT,eACY,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAgC,UAAA,KAAA4L,6CAAA,mBAAuE;UAkB7E5N,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,0BAC2B;UADqBD,EAAA,CAAAsD,gBAAA,wBAAAuK,8EAAArK,MAAA;YAAAxD,EAAA,CAAAe,aAAA,CAAAkM,GAAA;YAAAjN,EAAA,CAAA0D,kBAAA,CAAAqJ,GAAA,CAAAhD,SAAA,EAAAvG,MAAA,MAAAuJ,GAAA,CAAAhD,SAAA,GAAAvG,MAAA;YAAA,OAAAxD,EAAA,CAAAqB,WAAA,CAAAmC,MAAA;UAAA,EAAoB;UAClExD,EAAA,CAAAa,UAAA,wBAAAgN,8EAAA;YAAA7N,EAAA,CAAAe,aAAA,CAAAkM,GAAA;YAAA,OAAAjN,EAAA,CAAAqB,WAAA,CAAc0L,GAAA,CAAA9F,OAAA,EAAS;UAAA,EAAC;UAIhCjH,EAHM,CAAAG,YAAA,EAAiB,EACb,EACO,EACP;UAGVH,EAAA,CAAAgC,UAAA,KAAA8L,sDAAA,kCAAA9N,EAAA,CAAA+N,sBAAA,CAAkD;;;UArG7B/N,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAAmF,gBAAA,YAAA4H,GAAA,CAAA5G,yBAAA,CAAAvC,YAAA,CAAoD;UAClD5D,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACKJ,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAA2M,GAAA,CAAA3H,aAAA,CAAgB;UAQ5CpF,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAAmF,gBAAA,YAAA4H,GAAA,CAAA5G,yBAAA,CAAA/D,YAAA,CAAoD;UAKpDpC,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAmF,gBAAA,YAAA4H,GAAA,CAAA5G,yBAAA,CAAA9D,UAAA,CAAkD;UAMzCrC,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAmF,gBAAA,YAAA4H,GAAA,CAAA5G,yBAAA,CAAA5D,UAAA,CAAkD;UAC/BvC,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,YAAA2M,GAAA,CAAA1H,SAAA,CAAY;UAO/BrF,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAmF,gBAAA,YAAA4H,GAAA,CAAA5G,yBAAA,CAAAvD,OAAA,CAA+C;UAC7C5C,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACZJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UACXJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UAKbJ,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAmF,gBAAA,YAAA4H,GAAA,CAAA5G,yBAAA,CAAA1B,OAAA,CAA+C;UAC7CzE,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UASgCJ,EAAA,CAAAO,SAAA,IAAc;UAAdP,EAAA,CAAAI,UAAA,SAAA2M,GAAA,CAAAiB,QAAA,CAAc;UAyBnDhO,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAA2M,GAAA,CAAA1G,eAAA,CAAoB;UAmB/BrG,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAA2M,GAAA,CAAA/C,YAAA,CAA+B;UAAChK,EAAA,CAAAmF,gBAAA,SAAA4H,GAAA,CAAAhD,SAAA,CAAoB;UAAC/J,EAAA,CAAAI,UAAA,aAAA2M,GAAA,CAAAlD,QAAA,CAAqB;;;qBD9E5F9K,YAAY,EAAAuM,EAAA,CAAA2C,eAAA,EAAA3C,EAAA,CAAA4C,mBAAA,EAAA5C,EAAA,CAAA6C,qBAAA,EAAA7C,EAAA,CAAA8C,qBAAA,EACZ7O,mBAAmB,EACnBN,aAAa,EAAAqM,EAAA,CAAA+C,gBAAA,EACb7O,WAAW,EAAA8O,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,OAAA,EACXxP,cAAc,EAAAmM,EAAA,CAAAsD,iBAAA,EAAAtD,EAAA,CAAAuD,iBAAA,EACd3P,cAAc,EACdQ,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVZ,gBAAgB,EAAAsM,EAAA,CAAAwD,mBAAA,EAChBjP,kBAAkB,EAClBC,oBAAoB,EACpBV,cAAc;MAAA2P,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}