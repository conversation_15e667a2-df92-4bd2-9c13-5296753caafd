// 金額計算區塊樣式
.card {
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    }
}

// 營業稅區塊特殊樣式
.tax-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(to bottom, #0dcaf0, #0aa2c0);
        transition: width 0.3s ease;
    }

    &:hover::before {
        width: 6px;
    }
}

// 營業稅圖標容器
.tax-icon-wrapper {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0dcaf0, #0aa2c0);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(13, 202, 240, 0.3);
    transition: all 0.3s ease;

    i {
        font-size: 1.1rem;
        color: white !important;
    }

    &:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(13, 202, 240, 0.4);
    }
}

// 營業稅百分比徽章
.tax-percentage {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

// 營業稅金額動畫
.tax-amount {
    transition: all 0.3s ease;

    &:hover {
        transform: scale(1.05);
        color: #0aa2c0 !important;
    }
}

// 金額文字樣式
.text-primary {
    color: #0d6efd !important;
}

.text-info {
    color: #0dcaf0 !important;
}

// 分隔線樣式
hr {
    border-top: 2px solid #dee2e6;
    opacity: 0.5;
}

// 小計和總金額的樣式優化
.h5,
.h6 {
    transition: all 0.2s ease;
}

// 總金額區塊特殊效果
.text-primary.fw-bold {
    text-shadow: 0 1px 2px rgba(13, 110, 253, 0.1);
    transition: all 0.3s ease;

    &:hover {
        transform: scale(1.02);
        text-shadow: 0 2px 4px rgba(13, 110, 253, 0.2);
    }
}

// 資訊圖標樣式
.fa-info-circle {
    opacity: 0.7;
    transition: opacity 0.2s ease;

    &:hover {
        opacity: 1;
    }
}

// 響應式調整
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem !important;
    }

    .h4,
    .h5,
    .h6 {
        font-size: 1rem !important;
    }

    .tax-icon-wrapper {
        width: 35px;
        height: 35px;

        i {
            font-size: 1rem;
        }
    }

    .tax-section {
        padding: 1rem !important;

        &::before {
            width: 3px;
        }

        &:hover::before {
            width: 4px;
        }
    }
}