import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SharedModule } from '../components/shared.module';
import { CommonModule } from '@angular/common';
import { NbDatepickerModule, NbDialogService } from '@nebular/theme';
import { MessageService } from 'src/app/shared/services/message.service';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { BuildCaseService, HouseHoldMainService, HouseService } from 'src/services/api/services';
import { PetternHelper } from 'src/app/shared/helper/petternHelper';
// import { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';
import { Router } from '@angular/router';
import { BaseComponent } from '../components/base/baseComponent';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { Add<PERSON>ouse<PERSON>oldMain, EditHouseArgs, GetHouseListArgs, GetHouseListRes, TblHouse } from 'src/services/api/models';
import { concatMap, tap } from 'rxjs';
import { NbDateFnsDateModule } from '@nebular/date-fns';
import * as moment from 'moment';
import { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';
import { EnumHelper } from 'src/app/shared/helper/enumHelper';
import { EnumHouseType } from 'src/app/shared/enum/enumHouseType';
import { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';
import { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';
import { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';
import { LocalStorageService } from 'src/app/shared/services/local-storage.service';
import { STORAGE_KEY } from 'src/app/shared/constant/constant';
import { QuotationItem, CQuotationItemType } from 'src/app/models/quotation.model';
import { QuotationService } from 'src/app/services/quotation.service';


export interface selectItem {
  label: string,
  value: number | string,
  key?: string
}

// interface HouseDetailExtension {
//   changeStartDate: string;
//   changeEndDate: string;
// }
export interface SearchQuery {
  CBuildCaseSelected?: any | null;
  CHouseTypeSelected?: any | null;
  CBuildingNameSelected?: any | null;
  CHouseHoldSelected?: any | null;
  CPayStatusSelected?: any | null;
  CProgressSelected?: any | null;
  CSignStatusSelected?: any | null;
  CQuotationStatusSelected?: any | null;
  CIsEnableSeleted?: any | null;
  CFrom?: any | null;
  CTo?: any | null;
}

@Component({
  selector: 'ngx-household-management',
  templateUrl: './household-management.component.html',
  styleUrls: ['./household-management.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule,],
})

export class HouseholdManagementComponent extends BaseComponent implements OnInit {
  tempBuildCaseID: number = -1
  constructor(
    private _allow: AllowHelper,
    private enumHelper: EnumHelper,
    private dialogService: NbDialogService,
    private message: MessageService,
    private valid: ValidationHelper,
    private _houseService: HouseService,
    private _houseHoldMainService: HouseHoldMainService,
    private _buildCaseService: BuildCaseService,
    private pettern: PetternHelper,
    private router: Router,
    private _eventService: EventService,
    private _ultilityService: UtilityService,
    private quotationService: QuotationService,
    private http: HttpClient
  ) {
    super(_allow)
    this._eventService.receive().pipe(
      tap((res: IEvent) => {
        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {
          this.tempBuildCaseID = res.payload
        }
      })
    ).subscribe()
  }

  override pageFirst = 1;
  override pageSize = 10;
  override pageIndex = 1;
  override totalRecords = 0;

  statusOptions: selectItem[] = [
    {
      value: 0,
      key: 'allow',
      label: '允許',
    },
    {
      value: 1,
      key: 'not allowed',
      label: '不允許',
    }
  ]

  cIsEnableOptions = [
    {
      value: null,
      key: 'all',
      label: '全部',
    },
    {
      value: true,
      key: 'enable',
      label: '啟用',
    },
    {
      value: false,
      key: 'deactivate',
      label: '停用',
    }
  ]

  searchQuery: SearchQuery
  detailSelected: SearchQuery

  buildCaseOptions: any[] = [{ label: '全部', value: '' }]
  houseHoldOptions: any[] = [{ label: '全部', value: '' }]
  progressOptions: any[] = [{ label: '全部', value: -1 }]
  houseTypeOptions: any[] = [{ label: '全部', value: -1 }]
  payStatusOptions: any[] = [{ label: '全部', value: -1 }]
  signStatusOptions: any[] = [{ label: '全部', value: -1 }]
  quotationStatusOptions: any[] = [{ label: '全部', value: -1 }]

  options = {
    progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),
    payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),
    houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),
    quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus),
  }

  userBuildCaseOptions: any
  initDetail = {
    CHouseID: 0,
    CMail: "",
    CIsChange: false,
    CPayStatus: 0,
    CIsEnable: false,
    CCustomerName: "",
    CNationalID: "",
    CProgress: "",
    CHouseType: 0,
    CHouseHold: "",
    CPhone: ""
  }
  // 報價單相關
  quotationItems: QuotationItem[] = [];
  totalAmount: number = 0;
  // 新增：百分比費用設定
  additionalFeeName: string = '營業稅';  // 固定名稱
  additionalFeePercentage: number = 5;   // 固定5%
  additionalFeeAmount: number = 0;       // 百分比費用金額
  finalTotalAmount: number = 0;          // 最終總金額（含百分比費用）
  enableAdditionalFee: boolean = true;   // 固定啟用營業稅
  currentHouse: any = null;
  currentQuotationId: number = 0;
  isQuotationEditable: boolean = true; // 報價單是否可編輯

  override ngOnInit(): void {
    this.progressOptions = [
      ...this.progressOptions,
      ...this.enumHelper.getEnumOptions(EnumHouseProgress)
    ]
    this.houseTypeOptions = [
      ...this.houseTypeOptions,
      ...this.enumHelper.getEnumOptions(EnumHouseType)
    ]
    this.payStatusOptions = [
      ...this.payStatusOptions,
      ...this.enumHelper.getEnumOptions(EnumPayStatus)
    ]
    this.signStatusOptions = [
      ...this.signStatusOptions,
      ...this.enumHelper.getEnumOptions(EnumSignStatus)
    ]
    this.quotationStatusOptions = [
      ...this.quotationStatusOptions,
      ...this.enumHelper.getEnumOptions(EnumQuotationStatus)
    ]

    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null
      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined
      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != "") {
      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));
      this.searchQuery = {
        CBuildCaseSelected: null,
        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined
        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)
        //   : this.buildingSelectedOptions[0],
        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined
          ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value)
          : this.houseHoldOptions[0],
        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined
          ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value)
          : this.houseTypeOptions[0],
        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined
          ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value)
          : this.payStatusOptions[0],
        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined
          ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value)
          : this.progressOptions[0],
        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined
          ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value)
          : this.signStatusOptions[0],
        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined
          ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value)
          : this.quotationStatusOptions[0],
        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined
          ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value)
          : this.cIsEnableOptions[0],
        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined
          ? previous_search.CFrom
          : '',
        CTo: previous_search.CTo != null && previous_search.CTo != undefined
          ? previous_search.CTo
          : ''
      }
    }
    else {
      this.searchQuery = {
        CBuildCaseSelected: null,
        // CBuildingNameSelected: this.buildingSelectedOptions[0],
        CHouseHoldSelected: this.houseHoldOptions[0],
        CHouseTypeSelected: this.houseTypeOptions[0],
        CPayStatusSelected: this.payStatusOptions[0],
        CProgressSelected: this.progressOptions[0],
        CSignStatusSelected: this.signStatusOptions[0],
        CQuotationStatusSelected: this.quotationStatusOptions[0],
        CIsEnableSeleted: this.cIsEnableOptions[0],
        CFrom: '',
        CTo: ''
      }
    }
    this.getListBuildCase()
  }

  onSearch() {
    let sessionSave = {
      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,
      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,
      CFrom: this.searchQuery.CFrom,
      CTo: this.searchQuery.CTo,
      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,
      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,
      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,
      CPayStatusSelected: this.searchQuery.CPayStatusSelected,
      CProgressSelected: this.searchQuery.CProgressSelected,
      CSignStatusSelected: this.searchQuery.CSignStatusSelected,
      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected
    }
    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));
    this.getHouseList().subscribe()
  }

  pageChanged(newPage: number) {
    this.pageIndex = newPage;
    this.getHouseList().subscribe()
  }

  exportHouse() {
    if (this.searchQuery.CBuildCaseSelected.cID) {
      this._houseService.apiHouseExportHousePost$Json({
        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID
      }).subscribe(res => {
        if (res.Entries && res.StatusCode == 0) {
          this._ultilityService.downloadExcelFile(
            res.Entries, '戶別資訊範本'
          )
        } else {
          this.message.showErrorMSG(res.Message!);
        }
      })
    }
  }

  selectedFile: File | null = null;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;


  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
      this.importExcel();
    }
  }

  importExcel(): void {
    if (this.selectedFile) {
      const formData = new FormData();
      formData.append('CFile', this.selectedFile);
      this._houseService.apiHouseImportHousePost$Json({
        body: {
          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,
          CFile: this.selectedFile
        }
      }).subscribe(res => {
        if (res.StatusCode === 0) {
          this.message.showSucessMSG(res.Message!);
          this.getHouseList().subscribe()
        } else {
          this.message.showErrorMSG(res.Message!);
        }
      });
    }
  }


  getListHouseHold() {
    this._houseService.apiHouseGetListHouseHoldPost$Json({
      body: { CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.houseHoldOptions = [{
          value: '', label: '全部'
        }, ...res.Entries.map(e => {
          return { value: e, label: e }
        })]
        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null
          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined
          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != "") {
          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));
          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {
            let index = this.houseHoldOptions.findIndex((x: any) => x.value == previous_search.CHouseHoldSelected.value)
            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index]
          } else {
            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0]
          }
        }
      }
    })
  }

  houseList: any
  bodyRequest: GetHouseListArgs

  formatQuery() {
    this.bodyRequest = {
      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,
      PageIndex: this.pageIndex,
      PageSize: this.pageSize
    }
    if (this.searchQuery.CFrom && this.searchQuery.CTo) {
      this.bodyRequest['CFloor'] = { CFrom: this.searchQuery.CFrom, CTo: this.searchQuery.CTo }
    }
    if (this.searchQuery.CHouseHoldSelected) {
      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value
    }
    if (this.searchQuery.CHouseTypeSelected.value) {
      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value
    }
    if (typeof this.searchQuery.CIsEnableSeleted.value === "boolean") {
      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value
    }
    if (this.searchQuery.CPayStatusSelected.value) {
      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value
    }
    if (this.searchQuery.CProgressSelected.value) {
      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value
    }
    if (this.searchQuery.CSignStatusSelected.value) {
      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value
    }
    if (this.searchQuery.CQuotationStatusSelected.value) {
      this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value
    }

    return this.bodyRequest
  }

  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {
    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));
  }

  getHouseList() {
    return this._houseService.apiHouseGetHouseListPost$Json({
      body: this.formatQuery()
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.houseList = res.Entries;
          this.totalRecords = res.TotalItems!;
        }
      })
    )
  }


  userBuildCaseSelected: any
  onSelectionChangeBuildCase() {
    // this.getListBuilding()
    this.getListHouseHold()
    this.getHouseList().subscribe()
  }
  getListBuildCase() {
    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({
      body: {
        CIsPagi: false,
        CStatus: 1,
      }
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {
            return {
              CBuildCaseName: res.CBuildCaseName,
              cID: res.cID
            }
          }) : []

          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null
            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined
            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != "") {
            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));
            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {
              let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == previous_search.CBuildCaseSelected.cID)
              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index]
            } else {
              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]
            }
          }
          else {
            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]
          }
        }
      }),
      tap(() => {
        // this.getListBuilding()
        this.getListHouseHold()
        setTimeout(() => {
          this.getHouseList().subscribe();
        }, 500)
      })
    ).subscribe()
  }

  buildingSelected: any

  buildingSelectedOptions: any[] = [
    {
      value: '', label: '全部'
    }
  ]

  // getListBuilding() {
  //   this._houseService.apiHouseGetListBuildingPost$Json({
  //     body: {
  //       CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID
  //     }
  //   }).subscribe(res => {
  //     if (res.Entries && res.StatusCode == 0) {
  //       this.buildingSelectedOptions = [{
  //         value: '', label: '全部'
  //       }, ...res.Entries.map(e => {
  //         return { value: e, label: e }
  //       })]
  //       if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null
  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined
  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != "") {
  //         let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));
  //         if (previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined) {
  //           let index = this.buildingSelectedOptions.findIndex((x: any) => x.value == previous_search.CBuildingNameSelected.value)
  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[index]
  //         } else {
  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]
  //         }
  //       }
  //       else {
  //         this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]
  //       }
  //     }
  //   })
  // }

  houseDetail: TblHouse & {
    changeStartDate?: any;
    changeEndDate?: any
  }

  getHouseById(CID: any, ref: any) {
    this.detailSelected = {}
    this._houseService.apiHouseGetHouseByIdPost$Json({
      body: { CHouseID: CID }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.houseDetail = {
          ...res.Entries,
          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,
          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined,
        }

        if (res.Entries.CBuildCaseId) {
          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId)
        }
        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus)
        if (res.Entries.CHouseType) {
          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType)
        } else {
          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1]
        }
        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress)

        if (res.Entries.CBuildCaseId) {
          if (this.houseHoldMain) {
            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId
          }
        }
        this.dialogService.open(ref)
      }

    })
  }


  findItemInArray(array: any[], key: string, value: any) {
    return array.find(item => item[key] === value);
  }


  openModelDetail(ref: any, item: any) {
    this.getHouseById(item.CID, ref)
  }

  openModel(ref: any) {
    this.houseHoldMain = {
      CBuildingName: '',
      CFloor: undefined,
      CHouseHoldCount: undefined
    }
    this.dialogService.open(ref)
  }

  editHouseArgsParam: EditHouseArgs


  formatDate(CChangeDate: string): string {
    if (CChangeDate) {
      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')
    }
    return ''
  }

  onSubmitDetail(ref: any) {
    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '',
      this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '',

      this.editHouseArgsParam = {
        CCustomerName: this.houseDetail.CCustomerName,
        CHouseHold: this.houseDetail.CHousehold,
        CHouseID: this.houseDetail.CId,
        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,
        CIsChange: this.houseDetail.CIsChange,
        CIsEnable: this.houseDetail.CIsEnable,
        CMail: this.houseDetail.CMail,
        CNationalID: this.houseDetail.CNationalId,
        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,
        CPhone: this.houseDetail.CPhone,
        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,
        CChangeStartDate: this.houseDetail.CChangeStartDate,
        CChangeEndDate: this.houseDetail.CChangeEndDate
      }
    this.validation()
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return
    }

    this._houseService.apiHouseEditHousePost$Json({
      body: this.editHouseArgsParam
    }).pipe(
      tap(res => {
        if (res.StatusCode === 0) {
          this.message.showSucessMSG("執行成功");
          ref.close();
        } else {
          this.message.showErrorMSG(res.Message!);
          ref.close();
        }
      }),
      concatMap(() => this.getHouseList())
    ).subscribe();
  }


  onSubmit(ref: any) {
    let bodyReq: EditHouseArgs = {
      CCustomerName: this.houseDetail.CCustomerName,
      CHouseHold: this.houseDetail.CHousehold,
      CHouseID: this.houseDetail.CId,
      CHouseType: this.houseDetail.CHouseType,
      CIsChange: this.houseDetail.CIsChange,
      CIsEnable: this.houseDetail.CIsEnable,
      CMail: this.houseDetail.CMail,
      CNationalID: this.houseDetail.CNationalId,
      CPayStatus: this.houseDetail.CPayStatus,
      CPhone: this.houseDetail.CPhone,
      CProgress: this.houseDetail.CProgress,
    }
    this._houseService.apiHouseEditHousePost$Json({
      body: bodyReq
    }).subscribe(res => {
      if (res.StatusCode === 0) {
        this.message.showSucessMSG("執行成功");
        ref.close();
      }
    })

  }

  onClose(ref: any) {
    ref.close();
  }

  onNavidateId(type: any, id?: any) {
    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID
    this.router.navigate([`/pages/household-management/${type}`, idURL])
  }

  onNavidateBuildCaseIdHouseId(type: any, buildCaseId: any, houseId: any) {
    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId])
  }

  resetSecureKey(item: any) {
    if (confirm("您想重設密碼嗎？")) {
      this._houseService.apiHouseResetHouseSecureKeyPost$Json({
        body: item.CID
      }).subscribe(res => {
        if (res.StatusCode == 0) {
          this.message.showSucessMSG("執行成功");
        }
      })
    }
  }

  houseHoldMain: AddHouseHoldMain

  validation() {
    this.valid.clear();
    this.valid.required('[建案名稱]', this.houseDetail.CId)
    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold)
    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50)
    this.valid.required('[樓層]', this.houseDetail.CFloor)
    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50)
    // if (this.editHouseArgsParam.CNationalID) {
    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)
    // }
    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern)
    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone)
    this.valid.required('[進度]', this.editHouseArgsParam.CProgress)
    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value)
    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value)
    if (this.houseDetail.CChangeStartDate) {
      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate)
    }
    if (this.houseDetail.CChangeEndDate) {
      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate)
    }
    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '')
  }

  validationHouseHoldMain() {
    this.valid.clear();
    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID)
    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName)
    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10)
    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100)
    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100)
  }


  addHouseHoldMain(ref: any) {
    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID,
      this.validationHouseHoldMain()
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return
    }
    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({
      body: this.houseHoldMain
    }).pipe(
      tap(res => {
        if (res.StatusCode === 0) {
          this.message.showSucessMSG("執行成功");
          ref.close();
        }
      }),
      concatMap(() => this.getHouseList())
    ).subscribe();
  }  // 開啟報價單對話框
  async openQuotation(dialog: any, item: any) {
    this.currentHouse = item;
    this.quotationItems = [];
    this.totalAmount = 0;
    this.currentQuotationId = 0; // 重置報價單ID
    this.isQuotationEditable = true; // 預設可編輯
    // 重置百分比費用設定（固定營業稅5%）
    this.additionalFeeName = '營業稅';
    this.additionalFeePercentage = 5;
    this.additionalFeeAmount = 0;
    this.finalTotalAmount = 0;
    this.enableAdditionalFee = true;

    // 載入現有報價資料
    try {
      const response = await this.quotationService.getQuotationByHouseId(item.CID).toPromise();

      if (response && response.StatusCode === 0 && response.Entries) {
        // 保存當前的報價單ID
        this.currentQuotationId = response.Entries.CQuotationVersionId || 0;
        // 根據 cQuotationStatus 決定是否可編輯
        if (response.Entries.CQuotationStatus === 2) { // 2: 已報價
          this.isQuotationEditable = false;
        } else {
          this.isQuotationEditable = true;
        }

        // 載入額外費用設定（固定營業稅5%，不從後端載入）
        this.enableAdditionalFee = true;
        this.additionalFeeName = '營業稅';
        this.additionalFeePercentage = 5;

        // 檢查 Entries 是否有 Items 陣列
        if (response.Entries.Items && Array.isArray(response.Entries.Items)) {
          // 將 API 回傳的資料轉換為 QuotationItem 格式
          this.quotationItems = response.Entries.Items.map((entry: any) => ({
            cHouseID: response.Entries.CHouseID || item.CID,
            cQuotationID: response.Entries.CQuotationID,
            cItemName: entry.CItemName || '',
            cUnit: entry.CUnit || '',
            cUnitPrice: entry.CUnitPrice || 0,
            cCount: entry.CCount || 1,
            cStatus: entry.CStatus || 1,
            CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,
            cRemark: entry.CRemark || '',
            cQuotationStatus: entry.CQuotationStatus
          }));
          this.calculateTotal();
        } else {

        }
      } else {

      }
    } catch (error) {
      console.error('載入報價資料失敗:', error);
    }

    this.dialogService.open(dialog, {
      context: item,
      closeOnBackdropClick: false
    });
  }

  // 產生新報價單
  createNewQuotation() {
    this.currentQuotationId = 0;
    this.quotationItems = [];
    this.isQuotationEditable = true;
    this.totalAmount = 0;
    this.finalTotalAmount = 0;
    this.additionalFeeAmount = 0;
    this.enableAdditionalFee = true;

    // 顯示成功訊息
    this.message.showSucessMSG('已產生新報價單，可開始編輯');
  }
  // 新增自定義報價項目
  addQuotationItem() {
    this.quotationItems.push({
      cHouseID: this.currentHouse?.CID || 0,
      cItemName: '',
      cUnit: '',
      cUnitPrice: 0,
      cCount: 1,
      cStatus: 1,
      CQuotationItemType: CQuotationItemType.自定義,
      cRemark: ''
    });
  }
  // 載入客變需求
  async loadDefaultItems() {
    try {
      if (!this.currentHouse?.CID) {
        this.message.showErrorMSG('請先選擇戶別');
        return;
      }

      const request = {
        CBuildCaseID: this.searchQuery?.CBuildCaseSelected?.cID || 0,
        CHouseID: this.currentHouse.CID
      };

      const response = await this.quotationService.loadDefaultItems(request).toPromise();
      if (response?.success && response.data) {
        const defaultItems = response.data.map((x: any) => ({
          cQuotationID: x.CQuotationID,
          cHouseID: this.currentHouse?.CID,
          cItemName: x.CItemName,
          cUnit: x.CUnit || '',
          cUnitPrice: x.CUnitPrice,
          cCount: x.CCount,
          cStatus: x.CStatus,
          CQuotationItemType: CQuotationItemType.客變需求,
          cRemark: x.CRemark
        }));
        this.quotationItems.push(...defaultItems);
        this.calculateTotal();
        this.message.showSucessMSG('載入客變需求成功');
      } else {
        this.message.showErrorMSG(response?.message || '載入客變需求失敗');
      }
    } catch (error) {
      console.error('載入客變需求錯誤:', error);
      this.message.showErrorMSG('載入客變需求失敗');
    }
  }

  // 載入選樣資料
  async loadRegularItems() {
    try {
      if (!this.currentHouse?.CID) {
        this.message.showErrorMSG('請先選擇戶別');
        return;
      }

      const request = {
        CBuildCaseID: this.searchQuery?.CBuildCaseSelected?.cID || 0,
        CHouseID: this.currentHouse.CID
      };

      const response = await this.quotationService.loadRegularItems(request).toPromise();
      if (response?.success && response.data) {
        const regularItems = response.data.map((x: any) => ({
          cQuotationID: x.CQuotationID,
          cHouseID: this.currentHouse?.CID,
          cItemName: x.CItemName,
          cUnit: x.CUnit || '',
          cUnitPrice: x.CUnitPrice,
          cCount: x.CCount,
          cStatus: x.CStatus,
          CQuotationItemType: CQuotationItemType.選樣, // 選樣資料
          cRemark: x.CRemark || ''
        }));
        this.quotationItems.push(...regularItems);
        this.calculateTotal();
        this.message.showSucessMSG('載入選樣資料成功');
      } else {
        this.message.showErrorMSG(response?.message || '載入選樣資料失敗');
      }
    } catch (error) {
      console.error('載入選樣資料錯誤:', error);
      this.message.showErrorMSG('載入選樣資料失敗');
    }
  }

  // 移除報價項目
  removeQuotationItem(index: number) {
    const item = this.quotationItems[index];
    this.quotationItems.splice(index, 1);
    this.calculateTotal();
  }

  // 計算總金額
  calculateTotal() {
    this.totalAmount = this.quotationItems.reduce((sum, item) => {
      return sum + (item.cUnitPrice * item.cCount);
    }, 0);
    this.calculateFinalTotal();
  }

  // 計算百分比費用和最終總金額（固定營業稅5%）
  calculateFinalTotal() {
    // 固定計算營業稅5%
    this.additionalFeeAmount = Math.round(this.totalAmount * 0.05);
    this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;
  }

  // 格式化金額
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('zh-TW', {
      style: 'currency',
      currency: 'TWD',
      minimumFractionDigits: 0
    }).format(amount);
  }



  // 儲存報價單
  async saveQuotation(ref: any) {
    if (this.quotationItems.length === 0) {
      this.message.showErrorMSG('請先新增報價項目');
      return;
    }

    // 驗證必填欄位 (調整：允許單價和數量為負數)
    const invalidItems = this.quotationItems.filter(item =>
      !item.cItemName.trim()
    );

    if (invalidItems.length > 0) {
      this.message.showErrorMSG('請確認所有項目名稱都已正確填寫');
      return;
    } try {
      const request = {
        houseId: this.currentHouse.CID,
        items: this.quotationItems,
        quotationId: this.currentQuotationId, // 傳遞當前的報價單ID
        // 額外費用相關欄位
        cShowOther: this.enableAdditionalFee, // 啟用額外費用
        cOtherName: this.additionalFeeName,   // 額外費用名稱
        cOtherPercent: this.additionalFeePercentage // 額外費用百分比
      };

      const response = await this.quotationService.saveQuotation(request).toPromise();
      if (response?.success) {
        this.message.showSucessMSG('報價單儲存成功');
        ref.close();
      } else {
        this.message.showErrorMSG(response?.message || '儲存失敗');
      }
    } catch (error) {
      this.message.showErrorMSG('報價單儲存失敗');
    }
  }

  // 匯出報價單
  async exportQuotation() {
    try {
      const blob: Blob | undefined = await this.quotationService.exportQuotation(this.currentHouse.CID).toPromise();
      if (blob) {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `報價單_${this.currentHouse.CHouseHold}_${this.currentHouse.CFloor}樓.pdf`;
        link.click();
        window.URL.revokeObjectURL(url);
      } else {
        this.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');
      }
    } catch (error) {
      this.message.showErrorMSG('匯出報價單失敗');
    }
  }

  // 列印報價單
  async printQuotation() {
    if (this.quotationItems.length === 0) {
      this.message.showErrorMSG('沒有可列印的報價項目');
      return;
    }

    try {
      // 建立列印內容
      const printContent = await this.generatePrintContent();

      // 建立新的視窗進行列印
      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
      if (printWindow) {
        printWindow.document.open();
        printWindow.document.write(printContent);
        printWindow.document.close();

        // 等待內容載入完成後列印
        printWindow.onload = function () {
          setTimeout(() => {
            printWindow.print();
            // 列印後不自動關閉視窗，讓使用者可以預覽
          }, 500);
        };
      } else {
        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');
      }
    } catch (error) {
      console.error('列印報價單錯誤:', error);
      this.message.showErrorMSG('列印報價單時發生錯誤');
    }
  }

  // 產生列印內容
  private generatePrintContent(): string {
    // 使用內嵌模板
    const template = this.getQuotationTemplate();

    // 準備數據
    const currentDate = new Date().toLocaleDateString('zh-TW');
    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';

    // 生成項目HTML
    let itemsHtml = '';
    this.quotationItems.forEach((item, index) => {
      const subtotal = item.cUnitPrice * item.cCount;
      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);
      const unit = item.cUnit || '';
      itemsHtml += `
          <tr>
            <td class="text-center">${index + 1}</td>
            <td>${item.cItemName}</td>
            <td class="text-right">${this.formatCurrency(item.cUnitPrice)}</td>
            <td class="text-center">${unit}</td>
            <td class="text-center">${item.cCount}</td>
            <td class="text-right">${this.formatCurrency(subtotal)}</td>
            <td class="text-center">${quotationType}</td>
          </tr>
        `;
    });

    // 生成額外費用HTML
    const additionalFeeHtml = this.enableAdditionalFee ? `
        <div class="additional-fee">
          ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}
        </div>
      ` : '';

    // 替換模板中的占位符
    const html = template
      .replace(/{{buildCaseName}}/g, buildCaseName)
      .replace(/{{houseHold}}/g, this.currentHouse?.CHouseHold || '')
      .replace(/{{floor}}/g, this.currentHouse?.CFloor || '')
      .replace(/{{customerName}}/g, this.currentHouse?.CCustomerName || '')
      .replace(/{{printDate}}/g, currentDate)
      .replace(/{{itemsHtml}}/g, itemsHtml)
      .replace(/{{subtotalAmount}}/g, this.formatCurrency(this.totalAmount))
      .replace(/{{additionalFeeHtml}}/g, additionalFeeHtml)
      .replace(/{{totalAmount}}/g, this.formatCurrency(this.finalTotalAmount))
      .replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));

    return html;
  } catch(error) {
    console.error('讀取模板失敗:', error);
    throw new Error('無法讀取報價單模板文件，請檢查模板是否存在');
  }
}


  // 鎖定報價單
  async lockQuotation(ref: any) {
  if (this.quotationItems.length === 0) {
    this.message.showErrorMSG('請先新增報價項目');
    return;
  }

  if (!this.currentQuotationId) {
    this.message.showErrorMSG('無效的報價單ID');
    return;
  }

  try {
    const response = await this.quotationService.lockQuotation(this.currentQuotationId).toPromise();

    if (response.success) {
      this.message.showSucessMSG('報價單已成功鎖定');
      console.log('報價單鎖定成功:', {
        quotationId: this.currentQuotationId,
        message: response.message
      });
    } else {
      this.message.showErrorMSG(response.message || '報價單鎖定失敗');
      console.error('報價單鎖定失敗:', response.message);
    }

    ref.close();
  } catch (error) {
    this.message.showErrorMSG('報價單鎖定失敗');
    console.error('鎖定報價單錯誤:', error);
  }
}

// 取得報價類型文字
getQuotationTypeText(quotationType: CQuotationItemType): string {
  switch (quotationType) {
    case CQuotationItemType.客變需求:
      return '客變需求';
    case CQuotationItemType.自定義:
      return '自定義';
    case CQuotationItemType.選樣:
      return '選樣';
    default:
      return '未知';
  }
}

getQuotationStatusText(status: number): string {
  switch (status) {
    case EnumQuotationStatus.待報價:
      return '待報價';
    case EnumQuotationStatus.已報價:
      return '已報價';
    case EnumQuotationStatus.已簽回:
      return '已簽回';
    default:
      return '未知';
  }
}
}
